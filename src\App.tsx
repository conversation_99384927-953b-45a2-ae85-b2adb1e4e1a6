import React from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import Layout from './components/Layout/Layout';
import LoadingScreen from './components/LoadingScreen/LoadingScreen';
import WelcomeModal from './components/WelcomeModal/WelcomeModal';
import ChatBubble from './components/ChatBubble/ChatBubble';

// Pages
import Homepage from './pages/Homepage/Homepage';
import Accommodations from './pages/Accommodations/Accommodations';
import Experiences from './pages/Experiences/Experiences';
import Events from './pages/Events/Events';
import Community from './pages/Community/Community';
import Contact from './pages/Contact/Contact';
import AdminDashboard from './pages/Admin/AdminDashboard';
import NotFound from './pages/NotFound/NotFound';

const AppContent: React.FC = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');

  return (
    <div className="App">
      {!isAdminRoute && <LoadingScreen />}
      {!isAdminRoute && <WelcomeModal />}
      <Routes>
        {/* Admin routes - no main layout */}
        <Route path="/admin/*" element={<AdminDashboard />} />

        {/* Public routes - with main layout */}
        <Route path="/*" element={
          <Layout>
            <Routes>
              <Route path="/" element={<Homepage />} />
              <Route path="/accommodations" element={<Accommodations />} />
              <Route path="/experiences" element={<Experiences />} />
              <Route path="/events" element={<Events />} />
              <Route path="/community" element={<Community />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Layout>
        } />
      </Routes>
      {!isAdminRoute && <ChatBubble />}
    </div>
  );
};

function App() {
  return (
    <AppProvider>
      <Router>
        <AppContent />
      </Router>
    </AppProvider>
  );
}

export default App;
