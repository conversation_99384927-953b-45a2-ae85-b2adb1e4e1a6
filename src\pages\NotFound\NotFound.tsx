import React from 'react';
import { Link } from 'react-router-dom';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen bg-warm-beige flex items-center justify-center px-4">
      <div className="text-center max-w-2xl mx-auto">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-forest-green opacity-20 mb-4">404</div>
          <div className="text-6xl mb-6">🏕️🌲</div>
        </div>

        {/* Error Message */}
        <h1 className="text-4xl font-nature font-bold text-forest-green mb-4">
          Oops! You've Wandered Off the Trail
        </h1>
        <p className="text-xl text-stone-gray mb-8 max-w-lg mx-auto">
          It looks like you've taken a wrong turn in the wilderness. 
          Don't worry, even the best explorers sometimes lose their way!
        </p>

        {/* Action Buttons */}
        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center">
          <Link to="/" className="btn-primary inline-block">
            🏠 Return to Base Camp
          </Link>
          <Link to="/accommodations" className="btn-secondary inline-block">
            🏕️ Find Accommodations
          </Link>
        </div>

        {/* Helpful Links */}
        <div className="mt-12 pt-8 border-t border-sage-green border-opacity-30">
          <p className="text-stone-gray mb-4">Looking for something specific?</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link to="/experiences" className="text-forest-green hover:text-sage-green transition-colors">
              🥾 Experiences
            </Link>
            <Link to="/events" className="text-forest-green hover:text-sage-green transition-colors">
              📅 Events
            </Link>
            <Link to="/community" className="text-forest-green hover:text-sage-green transition-colors">
              📖 Community Stories
            </Link>
            <Link to="/contact" className="text-forest-green hover:text-sage-green transition-colors">
              📞 Contact Us
            </Link>
          </div>
        </div>

        {/* Fun Fact */}
        <div className="mt-8 p-6 bg-white rounded-lg shadow-lg">
          <h3 className="text-lg font-semibold text-forest-green mb-2">🌟 Did You Know?</h3>
          <p className="text-stone-gray text-sm">
            The word "Snawbra" comes from an ancient forest dialect meaning 
            "place where stories are born under starlight." Every path at our campsite 
            leads to a new adventure!
          </p>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
