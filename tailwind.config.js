/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'forest-green': '#2d5016',
        'sage-green': '#87a96b',
        'earth-brown': '#8b4513',
        'warm-beige': '#f5f5dc',
        'pine-green': '#355e3b',
        'moss-green': '#8fbc8f',
        'bark-brown': '#654321',
        'stone-gray': '#696969',
      },
      fontFamily: {
        'nature': ['Georgia', 'serif'],
        'modern': ['Inter', 'sans-serif'],
      },
      backgroundImage: {
        'hero-pattern': "linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4))",
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
}
