import React from 'react';

const AdminDashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-forest-green text-white min-h-screen">
          <div className="p-6">
            <h2 className="text-2xl font-bold">Admin Panel</h2>
          </div>
          <nav className="mt-6">
            <a href="#" className="block px-6 py-3 hover:bg-pine-green">Dashboard</a>
            <a href="#" className="block px-6 py-3 hover:bg-pine-green">Events Management</a>
            <a href="#" className="block px-6 py-3 hover:bg-pine-green">Community Stories</a>
            <a href="#" className="block px-6 py-3 hover:bg-pine-green">User Management</a>
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <h1 className="text-3xl font-bold text-forest-green mb-6">Dashboard</h1>
          <div className="text-stone-gray">
            <p>Admin dashboard functionality will be implemented in the next task...</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
