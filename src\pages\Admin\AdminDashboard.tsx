import React from 'react';
import AdminLayout from '../../components/Admin/AdminLayout';

const AdminDashboard: React.FC = () => {
  // Sample data for MVP dashboard
  const stats = [
    {
      name: 'Total Reservations',
      value: '127',
      change: '+12%',
      changeType: 'increase',
      icon: '📅'
    },
    {
      name: 'Occupancy Rate',
      value: '84%',
      change: '+5%',
      changeType: 'increase',
      icon: '🏕️'
    },
    {
      name: 'Revenue (This Month)',
      value: '$24,580',
      change: '+18%',
      changeType: 'increase',
      icon: '💰'
    },
    {
      name: 'Community Stories',
      value: '43',
      change: '+8',
      changeType: 'increase',
      icon: '📖'
    }
  ];

  const recentReservations = [
    { id: 1, guest: '<PERSON>', accommodation: 'Pine Tentalow', checkIn: '2024-12-28', status: 'Confirmed' },
    { id: 2, guest: '<PERSON> & <PERSON>.', accommodation: 'Cedar Bungalow', checkIn: '2024-12-30', status: 'Confirmed' },
    { id: 3, guest: '<PERSON>', accommodation: 'Forest Cabin', checkIn: '2025-01-02', status: 'Pending' },
    { id: 4, guest: '<PERSON>', accommodation: 'Pine Tentalow', checkIn: '2025-01-05', status: 'Confirmed' },
    { id: 5, guest: 'Alex Rodriguez', accommodation: 'Cedar Bungalow', checkIn: '2025-01-08', status: 'Confirmed' }
  ];

  const upcomingEvents = [
    { id: 1, name: 'Forest Photography Workshop', date: '2024-12-28', participants: 12 },
    { id: 2, name: 'New Year Celebration', date: '2024-12-31', participants: 45 },
    { id: 3, name: 'Winter Hiking Adventure', date: '2025-01-03', participants: 8 },
    { id: 4, name: 'Stargazing Session', date: '2025-01-05', participants: 20 }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Confirmed':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AdminLayout>
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-nature font-bold text-forest-green">Dashboard</h1>
          <p className="text-stone-gray mt-2">Welcome to your Snawbra Campsite admin panel</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat) => (
            <div key={stat.name} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-stone-gray">{stat.name}</p>
                  <p className="text-2xl font-bold text-forest-green">{stat.value}</p>
                </div>
                <div className="text-3xl">{stat.icon}</div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${
                  stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {stat.change} from last month
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recent Reservations */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Recent Reservations</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {recentReservations.map((reservation) => (
                  <div key={reservation.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{reservation.guest}</p>
                      <p className="text-sm text-stone-gray">{reservation.accommodation}</p>
                      <p className="text-sm text-stone-gray">Check-in: {reservation.checkIn}</p>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(reservation.status)}`}>
                      {reservation.status}
                    </span>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">View All Reservations</button>
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-forest-green">Upcoming Events</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium text-forest-green">{event.name}</p>
                      <p className="text-sm text-stone-gray">{event.date}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-forest-green">{event.participants}</p>
                      <p className="text-xs text-stone-gray">participants</p>
                    </div>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button className="w-full btn-secondary text-sm">Manage Events</button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-forest-green mb-6">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <button className="flex flex-col items-center p-4 bg-forest-green text-white rounded-lg hover:bg-pine-green transition-colors">
              <span className="text-2xl mb-2">➕</span>
              <span className="text-sm">New Reservation</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-sage-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🎉</span>
              <span className="text-sm">Add Event</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-moss-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">📧</span>
              <span className="text-sm">Send Newsletter</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-earth-brown text-white rounded-lg hover:bg-bark-brown transition-colors">
              <span className="text-2xl mb-2">📊</span>
              <span className="text-sm">View Reports</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-pine-green text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">🏕️</span>
              <span className="text-sm">Manage Rooms</span>
            </button>
            <button className="flex flex-col items-center p-4 bg-stone-gray text-white rounded-lg hover:bg-forest-green transition-colors">
              <span className="text-2xl mb-2">⚙️</span>
              <span className="text-sm">Settings</span>
            </button>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-forest-green mb-6">System Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-stone-gray">Booking System: Online</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-stone-gray">Payment Gateway: Active</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-stone-gray">Website: Operational</span>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
