import React from 'react';
import { Link } from 'react-router-dom';

const Homepage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section with Video Background */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Background Video */}
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src="/Assets/HerosectionVid.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Overlay */}
        <div className="hero-overlay"></div>

        {/* Content */}
        <div className="relative z-10 text-center text-white px-4">
          <h1 className="text-5xl md:text-7xl font-nature font-bold mb-6">
            Welcome to Snawbra Campsite
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            Discover the perfect blend of adventure and tranquility in nature's embrace
          </p>
          <Link to="/accommodations">
            <button className="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-transform duration-300">
              Explore Accommodations
            </button>
          </Link>
        </div>
      </section>

      {/* Value Proposition Section */}
      <section className="py-16 bg-warm-beige">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-nature font-bold text-forest-green mb-4">
              Your Gateway to Nature
            </h2>
            <p className="text-xl text-stone-gray max-w-3xl mx-auto">
              Experience the wilderness without sacrificing comfort. Our campsite offers
              the perfect balance of outdoor adventure and modern amenities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden shadow-lg">
                <img
                  src="/Assets/TentWHammock.jpg"
                  alt="Premium Accommodations"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-semibold text-forest-green mb-2">Premium Accommodations</h3>
              <p className="text-stone-gray">From cozy bungalows to luxury tentalows, find your perfect retreat.</p>
            </div>

            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden shadow-lg">
                <img
                  src="/Assets/CedarHikeWHorses.jpg"
                  alt="Nature Experiences"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-semibold text-forest-green mb-2">Nature Experiences</h3>
              <p className="text-stone-gray">Guided trails, wildlife watching, and outdoor adventures await.</p>
            </div>

            <div className="text-center">
              <div className="w-32 h-32 mx-auto mb-4 rounded-full overflow-hidden shadow-lg">
                <img
                  src="/Assets/CampingWFriends.jpg"
                  alt="Community Events"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-semibold text-forest-green mb-2">Community Events</h3>
              <p className="text-stone-gray">Join fellow nature lovers for workshops, festivals, and gatherings.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-nature font-bold text-center text-forest-green mb-12">
            Plan Your Perfect Getaway
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card p-6 text-center hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg overflow-hidden">
                <img
                  src="/Assets/InsideTentPOV.jpg"
                  alt="Accommodations"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green mb-2">Accommodations</h3>
              <p className="text-stone-gray text-sm mb-4">Find your perfect home away from home</p>
              <Link to="/accommodations">
                <button className="btn-secondary text-sm">View Options</button>
              </Link>
            </div>

            <div className="card p-6 text-center hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg overflow-hidden">
                <img
                  src="/Assets/HikeInForest.jpg"
                  alt="Experiences"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green mb-2">Experiences</h3>
              <p className="text-stone-gray text-sm mb-4">Discover outdoor adventures and activities</p>
              <Link to="/experiences">
                <button className="btn-secondary text-sm">Explore</button>
              </Link>
            </div>

            <div className="card p-6 text-center hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg overflow-hidden">
                <img
                  src="/Assets/Bonfire.jpg"
                  alt="Events"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green mb-2">Events</h3>
              <p className="text-stone-gray text-sm mb-4">Join our community gatherings and workshops</p>
              <Link to="/events">
                <button className="btn-secondary text-sm">See Calendar</button>
              </Link>
            </div>

            <div className="card p-6 text-center hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 mx-auto mb-4 rounded-lg overflow-hidden">
                <img
                  src="/Assets/PersonOnHammock.jpg"
                  alt="Community"
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green mb-2">Community</h3>
              <p className="text-stone-gray text-sm mb-4">Read stories from fellow adventurers</p>
              <Link to="/community">
                <button className="btn-secondary text-sm">Read Stories</button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Homepage;
