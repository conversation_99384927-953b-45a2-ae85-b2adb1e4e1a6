import React, { useState, useEffect, useRef } from 'react';
import { useAppContext } from '../../context/AppContext';
import ChatWindow from './ChatWindow';

const ChatBubble: React.FC = () => {
  const { isLoggedIn, isChatOpen, setIsChatOpen } = useAppContext();
  const [showAttention, setShowAttention] = useState(false);

  // Only show chat bubble after user is logged in
  if (!isLoggedIn) return null;

  // Show attention animation for first few seconds after login
  useEffect(() => {
    if (isLoggedIn) {
      setShowAttention(true);
      const timer = setTimeout(() => {
        setShowAttention(false);
      }, 5000); // Show attention for 5 seconds

      return () => clearTimeout(timer);
    }
  }, [isLoggedIn]);

  const handleChatToggle = () => {
    setIsChatOpen(!isChatOpen);
    setShowAttention(false);
  };

  return (
    <>
      <div
        className={`chat-bubble ${showAttention ? 'attention' : ''}`}
        onClick={handleChatToggle}
      >
        {isChatOpen ? (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        )}

        {showAttention && (
          <div className="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
        )}
      </div>

      {isChatOpen && <ChatWindow />}
    </>
  );
};

export default ChatBubble;
