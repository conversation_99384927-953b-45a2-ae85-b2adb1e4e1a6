import React, { useState } from 'react';
import { useAppContext } from '../../context/AppContext';

interface LoginFormData {
  email: string;
  password: string;
}

const WelcomeModal: React.FC = () => {
  const { showWelcomeModal, setShowWelcomeModal, setIsLoggedIn, updateUserPreferences } = useAppContext();
  const [showForm, setShowForm] = useState(false);
  const [isSignUp, setIsSignUp] = useState(false);
  const [formData, setFormData] = useState<LoginFormData>({ email: '', password: '' });
  const [isSubmitting, setIsSubmitting] = useState(false);

  if (!showWelcomeModal) return null;

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Simulate successful login/signup
    setIsLoggedIn(true);
    updateUserPreferences({
      userId: `user_${Date.now()}`,
      tripType: 'unknown'
    });

    // Mark as visited and close modal
    localStorage.setItem('snawbra_visited', 'true');
    setShowWelcomeModal(false);
    setIsSubmitting(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content max-w-lg">
        {!showForm ? (
          // Welcome Screen
          <div className="text-center">
            <div className="mb-6">
              <div className="w-20 h-20 bg-forest-green rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-3xl">🤖</span>
              </div>
              <h2 className="text-3xl font-nature font-bold text-forest-green mb-4">
                Welcome to Snawbra Campsite!
              </h2>
              <p className="text-lg text-stone-gray mb-6">
                I'm your personal AI guide. To help me tailor your perfect nature escape,
                let's get you signed in.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => {
                  setIsSignUp(true);
                  setShowForm(true);
                }}
                className="btn-primary flex-1 sm:flex-none"
              >
                Sign Up
              </button>
              <button
                onClick={() => {
                  setIsSignUp(false);
                  setShowForm(true);
                }}
                className="btn-secondary flex-1 sm:flex-none"
              >
                Log In
              </button>
            </div>

            <button
              onClick={() => setShowWelcomeModal(false)}
              className="mt-4 text-stone-gray hover:text-forest-green transition-colors text-sm"
            >
              Maybe later
            </button>
          </div>
        ) : (
          // Login/Signup Form
          <div>
            <div className="text-center mb-6">
              <h3 className="text-2xl font-nature font-bold text-forest-green mb-2">
                {isSignUp ? 'Create Account' : 'Welcome Back'}
              </h3>
              <p className="text-stone-gray">
                {isSignUp
                  ? 'Join our community of nature lovers'
                  : 'Sign in to continue your journey'
                }
              </p>
            </div>

            <form onSubmit={handleFormSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-stone-gray mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-stone-gray mb-1">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-forest-green focus:border-transparent"
                  placeholder="••••••••"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    {isSignUp ? 'Creating Account...' : 'Signing In...'}
                  </span>
                ) : (
                  isSignUp ? 'Create Account' : 'Sign In'
                )}
              </button>
            </form>

            <div className="mt-4 text-center">
              <button
                onClick={() => setShowForm(false)}
                className="text-stone-gray hover:text-forest-green transition-colors text-sm"
              >
                ← Back
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WelcomeModal;
