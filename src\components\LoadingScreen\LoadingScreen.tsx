import React, { useState, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';

const LoadingScreen: React.FC = () => {
  const { isLoading } = useAppContext();
  const [loadingText, setLoadingText] = useState('Preparing your nature escape...');
  const [progress, setProgress] = useState(0);

  const loadingMessages = [
    'Preparing your nature escape...',
    'Setting up your campfire...',
    'Arranging the perfect view...',
    'Gathering forest stories...',
    'Almost ready for adventure!'
  ];

  useEffect(() => {
    if (!isLoading) return;

    const messageInterval = setInterval(() => {
      setLoadingText(prev => {
        const currentIndex = loadingMessages.indexOf(prev);
        const nextIndex = (currentIndex + 1) % loadingMessages.length;
        return loadingMessages[nextIndex];
      });
    }, 1500);

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) return 0;
        return prev + Math.random() * 15;
      });
    }, 200);

    return () => {
      clearInterval(messageInterval);
      clearInterval(progressInterval);
    };
  }, [isLoading]);

  if (!isLoading) return null;

  return (
    <div className="loading-screen">
      <div className="text-center max-w-md mx-auto">
        {/* Logo Section */}
        <div className="loading-logo mb-12">
          <div className="relative">
            {/* Background circle with gradient */}
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-forest-green to-sage-green rounded-full flex items-center justify-center shadow-2xl">
              <div className="w-20 h-20 bg-gradient-to-br from-sage-green to-moss-green rounded-full flex items-center justify-center">
                <span className="text-3xl">🏕️</span>
              </div>
            </div>

            {/* Animated rings */}
            <div className="absolute inset-0 w-24 h-24 mx-auto">
              <div className="w-full h-full border-4 border-forest-green border-opacity-20 rounded-full animate-ping"></div>
            </div>
            <div className="absolute inset-0 w-24 h-24 mx-auto">
              <div className="w-full h-full border-2 border-sage-green border-opacity-40 rounded-full animate-pulse"></div>
            </div>
          </div>

          <h1 className="text-4xl font-nature font-bold text-forest-green mb-2">
            Snawbra Campsite
          </h1>
          <p className="text-sage-green text-lg font-medium">
            Where Nature Meets Comfort
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-forest-green to-sage-green rounded-full transition-all duration-300 ease-out"
              style={{ width: `${Math.min(progress, 100)}%` }}
            ></div>
          </div>
        </div>

        {/* Loading Animation */}
        <div className="flex justify-center space-x-3 mb-6">
          <div className="w-4 h-4 bg-forest-green rounded-full animate-bounce"></div>
          <div className="w-4 h-4 bg-sage-green rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-4 h-4 bg-moss-green rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>

        {/* Loading Text */}
        <p className="text-forest-green text-lg font-medium transition-all duration-500 ease-in-out">
          {loadingText}
        </p>

        {/* Nature Icons Animation */}
        <div className="mt-8 flex justify-center space-x-6 text-2xl">
          <span className="animate-pulse" style={{ animationDelay: '0s' }}>🌲</span>
          <span className="animate-pulse" style={{ animationDelay: '0.5s' }}>🦌</span>
          <span className="animate-pulse" style={{ animationDelay: '1s' }}>🌙</span>
          <span className="animate-pulse" style={{ animationDelay: '1.5s' }}>⭐</span>
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
