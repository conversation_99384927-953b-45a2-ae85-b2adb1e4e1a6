import React from 'react';
import { useAppContext } from '../../context/AppContext';

const LoadingScreen: React.FC = () => {
  const { isLoading } = useAppContext();

  if (!isLoading) return null;

  return (
    <div className="loading-screen">
      <div className="text-center">
        <div className="loading-logo mb-8">
          🏕️ Snawbra Campsite
        </div>
        <div className="flex justify-center space-x-2">
          <div className="w-3 h-3 bg-sage-green rounded-full animate-bounce"></div>
          <div className="w-3 h-3 bg-sage-green rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-3 h-3 bg-sage-green rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <p className="text-sage-green mt-4 text-lg">Preparing your nature escape...</p>
      </div>
    </div>
  );
};

export default LoadingScreen;
